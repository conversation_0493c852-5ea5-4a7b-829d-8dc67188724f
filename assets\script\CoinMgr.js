
cc.Class({
    extends: cc.Component,

    properties: {
        coinprefab: cc.Prefab,

        // 每组金币数量
        coinsPerGroup: {
            default: 8,
            tooltip: "每组生成的金币数量"
        },

        // 金币之间的间距
        coinSpacing: {
            default: 60,
            tooltip: "同组金币之间的水平间距"
        },

        // 金币组的生成模式
        groupPattern: {
            default: 4,
            type: cc.Enum({
                HORIZONTAL: 0,    // 水平一排
                CURVE_UP: 1,      // 向上弧形
                CURVE_DOWN: 2,    // 向下弧形
                ZIGZAG: 3,        // 之字形
                RANDOM: 4         // 随机模式
            }),
            tooltip: "金币组的排列模式"
        }
    },

    onLoad() {
        // 开始生成金币组
        this.spawnCoinGroup();
        this.schedule(this.spawnCoinGroup, 3);
    },


        setMoveSpeed(speed) {
        this.moveSpeed = speed;

        // 更新所有现有金币的移动速度
        this.node.children.forEach(child => {
            if (child.name === "coin") {
                let coinScript = child.getComponent('Coin');
                coinScript.moveSpeed = speed;
            }
        });
    },

    // 生成新的金币组
    spawnCoinGroup() {
        // 确定当前使用的模式
        let currentPattern = this.groupPattern;
        if (currentPattern === 4) { // RANDOM模式
            currentPattern = Math.floor(Math.random() * 4); // 随机选择0-3中的一种
        }

        // 计算起始位置
        let startX = cc.winSize.width / 2 + 100; // 从屏幕右侧开始
        let baseY = 0;
        
        this.generateCoins(startX, baseY, currentPattern);
    },

    // 生成金币的辅助方法
    generateCoins(startX, baseY, currentPattern) {
        // 生成一组金币
        for (let i = 0; i < this.coinsPerGroup; i++) {
            let coin = cc.instantiate(this.coinprefab);
            this.node.addChild(coin);

            // 计算每个金币的位置
            let coinX = startX + i * this.coinSpacing;
            let coinY = this.calculateCoinY(i, baseY, currentPattern);

            coin.x = coinX;
            coin.y = coinY;

            // 设置金币的移动速度
            let coinScript = coin.getComponent('Coin');
            coinScript.moveSpeed = this.moveSpeed;
        }

    },

    // 根据模式计算金币的Y坐标
    calculateCoinY(index, baseY, pattern) {
        switch (pattern) {
            case 0: // HORIZONTAL - 水平一排
                return baseY;

            case 1: // CURVE_UP - 向上弧形
                let progress1 = index / (this.coinsPerGroup - 1);
                return baseY + Math.sin(progress1 * Math.PI) * 60;

            case 2: // CURVE_DOWN - 向下弧形
                let progress2 = index / (this.coinsPerGroup - 1);
                return baseY - Math.sin(progress2 * Math.PI) * 60;

            case 3: // ZIGZAG - 之字形
                return baseY + (index % 2 === 0 ? 0 : 40);

            default:
                return baseY;
        }
    },

});
