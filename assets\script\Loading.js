
cc.Class({
    extends: cc.Component,

    properties: {
        progressBar: cc.ProgressBar,
        finishlabel:cc.Label,
        
    },

    // LIFE-CYCLE CALLBACKS:

     onLoad () {
        
        cc.resources.loadDir("",  (finish, total,item)=> {

//"") 会把 resources 目录下的所有子目录和文件全部加载。
//如果是cc.resources.loadDir("images", callback); 那么只会加载图片文件 

            this.progressBar.progress = finish / total;
            this.finishlabel.string = "已完成："+finish;
        
        },(error, assets) => {
            if (error) {
                cc.error(error);
                console.error("加载资源失败"+error);
                return;
            }
            // 加载完成后，跳转到游戏场景
           
            console.log("加载资源成功", assets);

             cc.director.loadScene("game");
        });
     },

    start () {

    },

    // update (dt) {},


});
