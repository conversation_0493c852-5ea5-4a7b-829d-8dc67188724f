cc.Class({
    extends: cc.Component,

    properties: {
        
        player: cc.Node,

     
        platformManager: cc.Node,

      
        coinManager: cc.Node,

   
        scoreManager: cc.Node,


        backgroundManager: cc.Node,

        // 暂停按钮
        pauseButton: cc.Node,

        // 暂停页面
        pauseAdvert: cc.Node,

        // 继续游戏按钮
        resumeButton: cc.Node,

        curScore: cc.Node,

        maxScore: cc.Node,

        gameSettlement: cc.Node,//游戏结算页面

        resurgencebutton: cc.Node,

        gostartbutton: cc.Node,

        // 按钮控制器
        buttonController: cc.Node,

        // 初始所有东西向左移动速度
        currentSpeed: 200,

        // 每秒速度增加
        speedIncreaseRate: 10,

        // 最大速度
        maxSpeed: 500
    },

    onLoad() {
        // 初始化物理引擎
        let physicsManager = cc.director.getPhysicsManager();
        physicsManager.enabled = true;
        physicsManager.gravity = cc.v2(0, -800); // 设置重力
        
        // 初始化游戏状态
        this.isGameOver = false;
        this.isPaused = false;

        this.gameSettlement.active = false;
        this.gameSettlement.active = false;

        // 获取平台管理器组件
        this.platformManagerComp = this.platformManager.getComponent('PlatformManager');

        // 获取金币管理器组件
        this.coinManagerComp = this.coinManager.getComponent('CoinMgr');

        // 获取玩家组件
        this.playerComp = this.player.getComponent('player');

        // 获取分数管理器组件
        this.scoreManagerComp = this.scoreManager.getComponent('ScoreManager');

        // 获取背景管理器组件
        this.backgroundManagerComp = this.backgroundManager.getComponent('BackgroundManager');

        // 初始化暂停功能
        
        this.pauseButton.on('click', this.pauseGame, this);
        // 绑定继续游戏
        this.resumeButton.on('click', this.resumeGame, this);

        //重开游戏
        this.resurgencebutton.on('click', this.restartGame, this);
        //回首页
        this.gostartbutton.on('click', this.gostartGame, this);
    },

    gostartGame() {
        cc.director.loadScene("start");
    },

    restartGame() {
        cc.director.loadScene("game");
    },


  

    
    update(dt) {
        if (this.isGameOver) return;

        // 逐渐增加速度
        this.currentSpeed = Math.min(this.maxSpeed, this.currentSpeed + this.speedIncreaseRate * dt);//返回两个参数中较小的
    
        this.platformManagerComp.setMoveSpeed(this.currentSpeed);
        this.coinManagerComp.setMoveSpeed(this.currentSpeed);
        this.backgroundManagerComp.setScrollSpeed(this.currentSpeed * 0.3); // 背景滚动速度稍慢

        // 检查游戏是否结束，玩家是否掉出了屏幕
         if (this.player && this.player.y < -cc.winSize.height / 2 - 100) {
            this.gameOver();
        }

    },
    
 
    
    gameOver() {
        if (this.isGameOver) return; // 防止多次调用

        this.isGameOver = true;

        // 通知分数管理器游戏结束
        if (this.scoreManagerComp) {
            this.scoreManagerComp.onGameOver();
        }

        // 停止平台移动
        this.platformManagerComp.setMoveSpeed(0);
        this.coinManagerComp.setMoveSpeed(0);
     
        // 停止背景滚动
        this.backgroundManagerComp.setScrollSpeed(0);
        
        // 隐藏按钮控制器
        if (this.buttonController) {
            this.buttonController.active = false;
        }
        
        this.gameSettlement.active = true;

        this.curScoreJs = this.curScore.getComponent(cc.Label);
        this.curScoreJs.string = this.scoreManagerComp.getScore();

        this.maxScoreJs = this.maxScore.getComponent(cc.Label);
        this.maxScoreJs.string = "最高分数: " + this.scoreManagerComp.getHighScore();
    },

    
    // 暂停游戏
    pauseGame() {
        if (this.isGameOver || this.isPaused) return;

        this.isPaused = true;

        // 暂停各个管理器组件
        this.platformManagerComp.enabled = false;
        this.coinManagerComp.enabled = false;
        this.backgroundManagerComp.enabled = false;

        // 隐藏按钮控制器
      
            this.buttonController.active = false;
        

        // 显示暂停页面
        if (this.pauseAdvert) {
            this.pauseAdvert.active = true;
        }
    },

    // 继续游戏
    resumeGame() {
        if (this.isGameOver || !this.isPaused) return;

        this.isPaused = false;

        // 恢复各个管理器组件
        this.platformManagerComp.enabled = true;
        this.coinManagerComp.enabled = true;
        this.backgroundManagerComp.enabled = true;

        // 显示按钮控制器
        if (this.buttonController) {
            this.buttonController.active = true;
        }

        // 隐藏暂停页面
        if (this.pauseAdvert) {
            this.pauseAdvert.active = false;
        }
    },

    onDestroy() {
        // 移除事件监听
        this.pauseButton.off('click', this.pauseGame, this);
        this.resumeButton.off('click', this.resumeGame, this);
        this.resurgencebutton.off('click', this.restartGame, this);
        this.gostartbutton.off('click', this.gostartGame, this);
    }
});
