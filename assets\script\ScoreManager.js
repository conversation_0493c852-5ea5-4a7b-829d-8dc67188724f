cc.Class({
    extends: cc.Component,

    properties: {
        // 分数显示标签
        scoreLabel: {
            default: null,
            type: cc.Label,
            tooltip: "用于显示分数的Label组件"
        },

        // 每个金币的分值
        coinValue: {
            default: 10,
            tooltip: "每个金币的分值"
        }
    },

    onLoad() {
        // 初始化分数数据
        this.score = 0;

        // 更新分数显示
        this.updateScoreDisplay();
    },

    start() {
        // 确保分数显示正确
        this.updateScoreDisplay();
    },

    // 添加分数
    addScore(points) {
        if (!points || points <= 0) return;

        // 增加分数
        this.score += points;

        // 更新显示
        this.updateScoreDisplay();

        // 触发分数变化事件（可供其他系统监听）
        this.node.emit('scoreChanged', this.score, points);

        return points;
    },

    // 添加金币分数（专门用于金币收集）
    addCoinScore() {
        return this.addScore(this.coinValue);
    },

    // 更新分数显示
    updateScoreDisplay() {
        this.scoreLabel.string = "分数: " + this.score;
    },

    // 获取当前分数
    getScore() {
        return this.score;
    },

    // 重置分数
    resetScore() {
        this.score = 0;
        this.updateScoreDisplay();

        // console.log("分数已重置");
        this.node.emit('scoreReset');
    },

    // 保存最高分
    saveHighScore() {
        let currentHighScore = cc.sys.localStorage.getItem('highScore') || 0;
        if (this.score > currentHighScore) {
            cc.sys.localStorage.setItem('highScore', this.score);
            // console.log(`新纪录! 最高分: ${this.score}`);
            return true;
        }
        return false;
    },

    // 获取最高分
    getHighScore() {
        return parseInt(cc.sys.localStorage.getItem('highScore')) || 0;
        
    },

    // 游戏结束时调用
    onGameOver() {
        console.log(`游戏结束! 最终分数: ${this.score}`);

        // 保存最高分
        let isNewRecord = this.saveHighScore();

        // 触发游戏结束事件
        this.node.emit('gameOver', {
            finalScore: this.score,
            isNewRecord: isNewRecord,
            highScore: this.getHighScore()
        });

        return {
            finalScore: this.score,
            isNewRecord: isNewRecord,
            highScore: this.getHighScore()
        };
    }
});
