cc.Class({
    extends: cc.Component,

    properties: {
        moveSpeed: 200,
        rotateSpeed: 360,
    },

    onLoad() {
        // 绑定碰撞事件
        this.node.on('onBeginContact', this.onBeginContact, this);

        // 获取GameManager引用
        this.gameManager = cc.find("Canvas").getComponent("GameManager");
    },

    update(dt) {
        // 检查游戏是否暂停
        if (this.gameManager && this.gameManager.isPaused) {
            return;
        }

        // 向左移动
        this.node.x -= this.moveSpeed * dt;

        // 当金币移出屏幕左侧时销毁
        if (this.node.x < -cc.winSize.width / 2 - 100) {
            this.node.destroy();
        }
    },
    
    onBeginContact(contact, selfCollider, otherCollider) {
        // 检查碰撞的是否是玩家
        if (otherCollider.node.name === "player") {

            // 播放收集金币的音效（如果有的话）
            // cc.audioEngine.playEffect(this.collectSound, false);

            // 直接获取ScoreManager组件增加得分
            let scoreManager = cc.find("Canvas/ScoreManager");
            let scoreManagerComp = scoreManager.getComponent("ScoreManager");
            scoreManagerComp.addCoinScore();
            // 销毁金币
            this.node.destroy();
        }
    },
    
    // 确保在销毁节点时移除事件监听
    onDestroy() {
        this.node.off('onBeginContact', this.onBeginContact, this);
    }
});