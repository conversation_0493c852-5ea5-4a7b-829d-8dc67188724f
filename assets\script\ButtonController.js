cc.Class({
    extends: cc.Component,

    properties: {
        // 跳跃按钮
        jumpButton: cc.Button,
        
        // 滑行按钮
        slideButton: cc.Button,
        
        // player节点引用
        playerNode: cc.Node
    },

 
    onLoad() {
        // 绑定按钮点击事件
        this.jumpButton.node.on('click', this.onJumpButtonClick, this);
        this.slideButton.node.on('click', this.onSlideButtonClick, this);
    },

    onJumpButtonClick() {
        let playerScript = this.playerNode.getComponent('Player');
        playerScript.jump();
    },

    onSlideButtonClick() {
        let playerScript = this.playerNode.getComponent('Player');
        playerScript.slide();
    },

    onDestroy() {
        // 移除按钮事件监听
        if (this.jumpButton && this.jumpButton.node) {
            this.jumpButton.node.off('click', this.onJumpButtonClick, this);
        }

        if (this.slideButton && this.slideButton.node) {
            this.slideButton.node.off('click', this.onSlideButtonClick, this);
        }
    }
});
